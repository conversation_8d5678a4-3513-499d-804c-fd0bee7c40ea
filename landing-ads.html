<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loma Bag - Túi <PERSON>i In Logo Giúp Tăng 15-30% Tỷ Lệ Chốt Đơn</title>
    <meta
      name="description"
      content="Xưởng sản xuất túi vải in logo từ 50 túi. Giúp shop online tăng tỷ lệ chốt đơn, tăng giá trị đơn hàng. Miễn phí thiết kế mockup, cọc 500k hoàn lại 100%."
    />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SwiperJS for Gallery Slider -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/swiper/swiper-bundle.min.css"
    />

    <style>
      /* Custom Styles */
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Be Vietnam Pro", sans-serif;
        background-color: #f9fafb;
        line-height: 1.7;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.4;
      }
      .cta-button {
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }
      .cta-button:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
      }
      .section-title {
        position: relative;
        padding-bottom: 1rem;
      }
      .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background-color: #ef4444;
        border-radius: 2px;
      }

      /* Enhanced Hero Section */
      .hero-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
      }
      .hero-gradient::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
      }

      /* Typing animation */
      .typed-cursor {
        opacity: 1;
        animation: blink 0.7s infinite;
      }
      @keyframes blink {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      /* Floating animation */
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }
      .float-animation {
        animation: float 3s ease-in-out infinite;
      }
      /* Modal */
      .modal {
        transition: opacity 0.3s ease;
        z-index: 9999;
      }
      .modal.show {
        display: flex !important;
        opacity: 1 !important;
      }
      .modal.hide {
        opacity: 0 !important;
      }
      .video-placeholder {
        aspect-ratio: 16 / 9;
        background-color: #111827; /* gray-900 */
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem;
        cursor: pointer;
        overflow: hidden;
        position: relative;
      }
      .play-button {
        width: 80px;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s ease, background-color 0.2s ease;
        z-index: 10;
      }
      .play-button:hover {
        transform: scale(1.1);
        background-color: rgba(239, 68, 68, 0.8);
      }
      .video-placeholder img {
        transition: transform 0.5s ease;
      }
      .video-placeholder:hover img {
        transform: scale(1.05);
      }
    </style>
  </head>
  <body class="text-gray-800">
    <!-- HEADER -->
    <header
      class="bg-white/95 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm border-b border-gray-100"
    >
      <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <!-- Logo & Badge -->
          <div class="flex items-center">
            <h1 class="text-xl md:text-2xl font-bold text-gray-800">
              Loma<span class="text-red-500">Bag</span>
            </h1>
            <span
              class="ml-2 md:ml-3 bg-red-100 text-red-600 text-xs font-bold px-2 py-1 rounded-full hidden sm:inline-block"
              >Xưởng Sản Xuất</span
            >
          </div>

          <!-- Mobile Menu Button -->
          <button
            id="mobile-menu-btn"
            class="md:hidden flex flex-col space-y-1 p-2"
          >
            <span
              class="w-6 h-0.5 bg-gray-600 transition-all duration-300"
            ></span>
            <span
              class="w-6 h-0.5 bg-gray-600 transition-all duration-300"
            ></span>
            <span
              class="w-6 h-0.5 bg-gray-600 transition-all duration-300"
            ></span>
          </button>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex items-center space-x-6 lg:space-x-8">
            <a
              href="#video-section"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Video</a
            >
            <a
              href="#gallery"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Mẫu Túi</a
            >
            <a
              href="#case-study"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Case Study</a
            >
            <a
              href="#pricing"
              class="text-gray-600 hover:text-red-500 font-medium transition-colors text-sm lg:text-base"
              >Bảng Giá</a
            >
          </nav>

          <!-- Desktop CTA -->
          <div class="hidden md:flex items-center space-x-3 lg:space-x-4">
            <div class="hidden lg:flex items-center text-sm text-gray-600">
              <span class="text-green-500 mr-1">📞</span>
              <span class="font-semibold">0901.234.567</span>
            </div>
            <a
              href="#form-bao-gia"
              class="cta-button bg-red-500 text-white font-bold py-2 px-4 lg:px-6 rounded-full hover:bg-red-600 text-sm"
            >
              🎁 Báo Giá
            </a>
          </div>

          <!-- Mobile CTA -->
          <a
            href="#form-bao-gia"
            class="md:hidden cta-button bg-red-500 text-white font-bold py-2 px-4 rounded-full hover:bg-red-600 text-sm"
          >
            Báo Giá
          </a>
        </div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="md:hidden absolute top-full left-0 right-0 bg-white shadow-lg border-t border-gray-100 transform -translate-y-full opacity-0 invisible transition-all duration-300"
        >
          <nav class="px-4 py-6 space-y-4">
            <a
              href="#video-section"
              class="block text-gray-700 hover:text-red-500 font-medium py-2 border-b border-gray-100"
              >📹 Video Xưởng</a
            >
            <a
              href="#gallery"
              class="block text-gray-700 hover:text-red-500 font-medium py-2 border-b border-gray-100"
              >🎨 Mẫu Túi</a
            >
            <a
              href="#case-study"
              class="block text-gray-700 hover:text-red-500 font-medium py-2 border-b border-gray-100"
              >📊 Case Study</a
            >
            <a
              href="#pricing"
              class="block text-gray-700 hover:text-red-500 font-medium py-2 border-b border-gray-100"
              >💰 Bảng Giá</a
            >
            <div class="pt-4 border-t border-gray-200">
              <div class="flex items-center text-sm text-gray-600 mb-3">
                <span class="text-green-500 mr-2">📞</span>
                <span class="font-semibold">Hotline: 0901.234.567</span>
              </div>
              <a
                href="#form-bao-gia"
                class="block w-full text-center bg-red-500 text-white font-bold py-3 rounded-lg hover:bg-red-600"
              >
                🎁 Nhận Báo Giá Miễn Phí
              </a>
            </div>
          </nav>
        </div>
      </div>
    </header>

    <!-- HERO SECTION -->
    <section class="hero-gradient text-white pt-32 pb-20 text-center relative">
      <div class="container mx-auto px-6 relative z-10">
        <!-- Floating icons -->
        <div class="absolute top-10 left-10 float-animation hidden lg:block">
          <div
            class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
          >
            <span class="text-2xl">🛍️</span>
          </div>
        </div>
        <div
          class="absolute top-20 right-20 float-animation hidden lg:block"
          style="animation-delay: 1s"
        >
          <div
            class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
          >
            <span class="text-xl">📈</span>
          </div>
        </div>

        <div class="max-w-4xl mx-auto">
          <h2 class="text-4xl md:text-6xl font-extrabold leading-tight mb-6">
            Đang chạy Ads mà khách cứ "xem rồi thoát"?<br />
            <span class="text-yellow-300">Thêm 1 món quà nhỏ để...</span><br />
            <span id="typing-effect" class="text-red-300"></span>
          </h2>
          <div
            class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20"
          >
            <p class="text-xl md:text-2xl font-semibold text-yellow-100 mb-4">
              💡 Sự thật: Bạn đã chi tiền cho Ads để khách click vào
            </p>
            <p class="text-lg md:text-xl text-white/90">
              Nhưng
              <strong class="text-yellow-300">90% khách xem rồi thoát</strong>
              mà không mua gì. Một chiếc
              <strong class="text-red-300">túi vải in logo làm quà tặng</strong>
              sẽ là "cú hích" cuối cùng khiến họ
              <strong class="text-green-300">chốt đơn ngay lập tức!</strong>
            </p>
          </div>
          <div
            class="flex flex-col sm:flex-row justify-center items-center gap-4"
          >
            <a
              href="#form-bao-gia"
              class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block w-full sm:w-auto"
            >
              🎁 Nhận Báo Giá Ngay
            </a>
            <a
              href="#case-study"
              class="cta-button bg-white/20 backdrop-blur-sm text-white border border-white/30 font-bold py-4 px-8 rounded-full text-lg hover:bg-white/30 inline-block w-full sm:w-auto"
            >
              📊 Xem Case Thực Tế
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- VIDEO SECTION -->
    <section
      id="video-section"
      class="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-blue-50"
    >
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-3xl md:text-4xl font-bold">
            📹 Trăm Nghe Không Bằng Một Thấy
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-lg">
            Xem video thực tế về quy trình sản xuất và chất lượng sản phẩm. Đây
            là bằng chứng cụ thể về sự chuyên nghiệp của Loma Bag.
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto mb-12">
          <!-- Video 1: Product Showcase -->
          <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="video-placeholder" data-video-id="dQw4w9WgXcQ">
              <img
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1280&h=720&fit=crop&crop=center"
                alt="Các mẫu túi vải in logo hot nhất"
                class="absolute inset-0 w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black/30"></div>
              <div class="play-button">
                <svg
                  class="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div class="absolute bottom-4 left-4 right-4 text-white">
                <div
                  class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold inline-block mb-2"
                >
                  🔥 Trending
                </div>
                <h4 class="text-lg font-bold">
                  Top 10 Mẫu Túi Bán Chạy Nhất 2024
                </h4>
                <p class="text-sm text-white/90">
                  Xem các mẫu túi được shop yêu thích nhất
                </p>
              </div>
            </div>
          </div>

          <!-- Video 2: Factory Tour -->
          <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="video-placeholder" data-video-id="dQw4w9WgXcQ">
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1280&h=720&fit=crop&crop=center"
                alt="Tham quan xưởng sản xuất Loma Bag"
                class="absolute inset-0 w-full h-full object-cover"
              />
              <div class="absolute inset-0 bg-black/30"></div>
              <div class="play-button">
                <svg
                  class="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
              </div>
              <div class="absolute bottom-4 left-4 right-4 text-white">
                <div
                  class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold inline-block mb-2"
                >
                  🏭 Behind The Scenes
                </div>
                <h4 class="text-lg font-bold">Tham Quan Xưởng Sản Xuất</h4>
                <p class="text-sm text-white/90">
                  Quy trình sản xuất chuyên nghiệp từ A-Z
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-red-600">1000+</div>
            <div class="text-sm text-gray-600">Video đã xem</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-blue-600">98%</div>
            <div class="text-sm text-gray-600">Khách hài lòng</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-green-600">24h</div>
            <div class="text-sm text-gray-600">Phản hồi nhanh</div>
          </div>
          <div class="text-center p-4 bg-white rounded-lg shadow-md">
            <div class="text-2xl font-bold text-purple-600">500+</div>
            <div class="text-sm text-gray-600">Shop tin tưởng</div>
          </div>
        </div>
      </div>
    </section>

    <!-- PRICING & PLANS SECTION -->
    <section id="pricing" class="py-16 md:py-24 bg-gray-50">
      <div class="container mx-auto px-4 sm:px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-2xl md:text-4xl font-bold">
            💰 Bảng Giá & Phương Án An Toàn
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
            Bạn không cần đầu tư lớn ngay từ đầu. Chúng tôi có phương án để bạn
            thử nghiệm với rủi ro = 0.
          </p>
        </div>

        <!-- Price Range -->
        <div
          class="text-center mb-12 bg-white p-6 rounded-xl shadow-lg max-w-3xl mx-auto border border-red-100"
        >
          <h4 class="text-xl md:text-2xl font-semibold mb-4">
            📊 Mức giá tham khảo
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div class="p-4 bg-green-50 rounded-lg">
              <p class="text-2xl font-bold text-green-600">25.000đ</p>
              <p class="text-sm text-gray-600">Túi cơ bản (1000+ túi)</p>
            </div>
            <div class="p-4 bg-blue-50 rounded-lg">
              <p class="text-2xl font-bold text-blue-600">45.000đ</p>
              <p class="text-sm text-gray-600">Túi đẹp (500+ túi)</p>
            </div>
            <div class="p-4 bg-purple-50 rounded-lg">
              <p class="text-2xl font-bold text-purple-600">65.000đ</p>
              <p class="text-sm text-gray-600">Túi cao cấp (100+ túi)</p>
            </div>
          </div>
          <p class="text-gray-500 text-sm md:text-base">
            <strong>Giá chính xác phụ thuộc:</strong> Số lượng, Mẫu túi, Kích
            thước, Hình in, Chất liệu...
          </p>
        </div>
        <!-- Plans -->
        <div class="grid md:grid-cols-2 gap-6 md:gap-8 max-w-6xl mx-auto">
          <!-- Plan 1: Test Package -->
          <div
            class="bg-white p-6 md:p-8 rounded-xl shadow-lg border-2 border-red-500 relative overflow-hidden"
          >
            <div
              class="absolute top-0 right-0 bg-red-500 text-white px-4 py-1 text-sm font-bold"
            >
              KHUYẾN NGHỊ
            </div>
            <div class="mb-6">
              <h4 class="text-xl md:text-2xl font-bold text-red-600 mb-2">
                🎯 Gói Thử Nghiệm (Rủi Ro = 0)
              </h4>
              <p class="text-gray-600 text-sm md:text-base">
                Dành cho shop muốn test hiệu quả trước khi đầu tư lớn
              </p>
            </div>

            <div class="space-y-4 mb-6">
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800"
                    >MIỄN PHÍ thiết kế Mockup</strong
                  >
                  <p class="text-sm text-gray-600">
                    Xem trước túi với logo của bạn, không mất phí
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">Làm 1 túi mẫu thực tế</strong>
                  <p class="text-sm text-gray-600">
                    Cọc 500k → Dùng để chụp ảnh, quay video, test chất lượng
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">HOÀN CỌC 100%</strong>
                  <p class="text-sm text-gray-600">
                    Đặt từ 100 túi → Hoàn lại 500k = MIỄN PHÍ hoàn toàn
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">Bắt đầu từ 50 túi</strong>
                  <p class="text-sm text-gray-600">
                    Số lượng nhỏ để test thị trường, giảm rủi ro
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-red-50 p-4 rounded-lg mb-6">
              <p class="text-sm text-red-800">
                <strong>💡 Phù hợp với:</strong> Shop mới bắt đầu, muốn test
                hiệu quả túi vải trước khi scale up
              </p>
            </div>

            <a
              href="#form-bao-gia"
              class="cta-button block w-full text-center bg-red-500 text-white font-bold py-3 px-6 rounded-full hover:bg-red-600 text-sm md:text-base"
            >
              🎁 Yêu Cầu Làm Mẫu MIỄN PHÍ
            </a>
          </div>

          <!-- Plan 2: Scale Package -->
          <div
            class="bg-white p-6 md:p-8 rounded-xl shadow-lg border-2 border-blue-300"
          >
            <div class="mb-6">
              <h4 class="text-xl md:text-2xl font-bold text-blue-600 mb-2">
                🚀 Gói Scale Up (Tối Ưu Chi Phí)
              </h4>
              <p class="text-gray-600 text-sm md:text-base">
                Dành cho shop có doanh thu ổn định, muốn giá tốt nhất
              </p>
            </div>

            <div class="space-y-4 mb-6">
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">Giá gốc xưởng</strong>
                  <p class="text-sm text-gray-600">
                    Càng đặt nhiều, giá càng rẻ. Tiết kiệm 20-40%
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">Ưu tiên sản xuất</strong>
                  <p class="text-sm text-gray-600">
                    Đơn hàng được xử lý nhanh, giao đúng hạn
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">MIỄN PHÍ ship toàn quốc</strong>
                  <p class="text-sm text-gray-600">
                    Tiết kiệm thêm 50-100k phí vận chuyển
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <span class="text-green-500 font-bold mr-3 mt-1">✅</span>
                <div>
                  <strong class="text-gray-800">Tư vấn chiến lược</strong>
                  <p class="text-sm text-gray-600">
                    Gợi ý mẫu túi best-seller theo ngành hàng
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-blue-50 p-4 rounded-lg mb-6">
              <p class="text-sm text-blue-800">
                <strong>💡 Phù hợp với:</strong> Shop có doanh thu 50+
                triệu/tháng, muốn tối ưu chi phí marketing
              </p>
            </div>

            <a
              href="#form-bao-gia"
              class="cta-button block w-full text-center bg-blue-600 text-white font-bold py-3 px-6 rounded-full hover:bg-blue-700 text-sm md:text-base"
            >
              💰 Nhận Báo Giá Sỉ Ngay
            </a>
          </div>
        </div>

        <!-- Process Steps -->
        <div class="mt-16 max-w-4xl mx-auto">
          <h4 class="text-xl md:text-2xl font-bold text-center mb-8">
            🔄 Quy Trình Triển Khai Đơn Giản
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-white rounded-lg shadow-md">
              <div
                class="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold"
              >
                1
              </div>
              <h5 class="font-semibold mb-2">Gửi Logo</h5>
              <p class="text-sm text-gray-600">
                Gửi logo, ý tưởng → Nhận mockup miễn phí trong 2h
              </p>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-md">
              <div
                class="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold"
              >
                2
              </div>
              <h5 class="font-semibold mb-2">Làm Mẫu</h5>
              <p class="text-sm text-gray-600">
                Cọc 500k → Nhận túi mẫu thực tế trong 3-5 ngày
              </p>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-md">
              <div
                class="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold"
              >
                3
              </div>
              <h5 class="font-semibold mb-2">Test & Đặt Hàng</h5>
              <p class="text-sm text-gray-600">
                Dùng mẫu test → Hài lòng thì đặt từ 50-100 túi
              </p>
            </div>
            <div class="text-center p-4 bg-white rounded-lg shadow-md">
              <div
                class="w-12 h-12 bg-purple-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold"
              >
                4
              </div>
              <h5 class="font-semibold mb-2">Nhận Hàng</h5>
              <p class="text-sm text-gray-600">
                Giao hàng 7-10 ngày → Bắt đầu tặng khách
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- GALLERY SECTION -->
    <section id="gallery" class="py-16 md:py-24 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-3xl md:text-4xl font-bold">
            🎨 Thư Viện Mẫu Túi Vải In Logo
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-lg">
            Hơn 1000+ mẫu túi đã được sản xuất cho các shop online. Click để xem
            chi tiết từng loại túi và ý tưởng thiết kế.
          </p>
        </div>

        <!-- Featured Gallery Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <!-- Túi Tote - Most Popular -->
          <div
            class="gallery-item group rounded-xl overflow-hidden shadow-xl relative cursor-pointer transform hover:scale-105 transition-all duration-300"
            data-category="tote"
          >
            <div
              class="aspect-[4/5] bg-gradient-to-br from-orange-100 to-red-100 flex items-center justify-center relative"
            >
              <img
                src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop&crop=center"
                alt="Túi tote canvas in logo"
                class="w-full h-full object-cover"
              />
              <div
                class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold"
              >
                🔥 Phổ biến nhất
              </div>
            </div>
            <div
              class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              <h4 class="text-white text-xl font-bold mb-2">Túi Tote Canvas</h4>
              <p class="text-white/90 text-sm mb-3">
                Phù hợp: Shop thời trang, mỹ phẩm, phụ kiện
              </p>
              <div class="flex items-center text-yellow-400 text-sm">
                <span class="mr-2">⭐⭐⭐⭐⭐</span>
                <span class="text-white">500+ shop đã chọn</span>
              </div>
            </div>
          </div>

          <!-- Túi Dây Rút -->
          <div
            class="gallery-item group rounded-xl overflow-hidden shadow-xl relative cursor-pointer transform hover:scale-105 transition-all duration-300"
            data-category="day-rut"
          >
            <div
              class="aspect-[4/5] bg-gradient-to-br from-green-100 to-blue-100 flex items-center justify-center relative"
            >
              <img
                src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop&crop=center"
                alt="Túi dây rút thể thao"
                class="w-full h-full object-cover"
              />
              <div
                class="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold"
              >
                💪 Thể thao
              </div>
            </div>
            <div
              class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              <h4 class="text-white text-xl font-bold mb-2">Túi Dây Rút</h4>
              <p class="text-white/90 text-sm mb-3">
                Phù hợp: Shop thể thao, phụ kiện, đồ gia dụng
              </p>
              <div class="flex items-center text-yellow-400 text-sm">
                <span class="mr-2">⭐⭐⭐⭐⭐</span>
                <span class="text-white">200+ shop đã chọn</span>
              </div>
            </div>
          </div>

          <!-- Túi Hộp Cao Cấp -->
          <div
            class="gallery-item group rounded-xl overflow-hidden shadow-xl relative cursor-pointer transform hover:scale-105 transition-all duration-300"
            data-category="hop"
          >
            <div
              class="aspect-[4/5] bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center relative"
            >
              <img
                src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop&crop=center"
                alt="Túi hộp cao cấp"
                class="w-full h-full object-cover"
              />
              <div
                class="absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold"
              >
                👑 Cao cấp
              </div>
            </div>
            <div
              class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              <h4 class="text-white text-xl font-bold mb-2">
                Túi Hộp Đứng Form
              </h4>
              <p class="text-white/90 text-sm mb-3">
                Phù hợp: Shop mỹ phẩm cao cấp, trang sức
              </p>
              <div class="flex items-center text-yellow-400 text-sm">
                <span class="mr-2">⭐⭐⭐⭐⭐</span>
                <span class="text-white">150+ shop đã chọn</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Quality Showcase -->
        <div
          class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mb-8"
        >
          <div class="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h4 class="text-2xl font-bold text-gray-800 mb-4">
                🎯 Chất Lượng In Ấn Sắc Nét
              </h4>
              <div class="space-y-3">
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700"
                    >Công nghệ in lụa cao cấp, màu sắc bền đẹp</span
                  >
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700"
                    >Logo sắc nét, không bong tróc sau nhiều lần giặt</span
                  >
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700"
                    >Có thể in 1 mặt hoặc 2 mặt theo yêu cầu</span
                  >
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-3">✅</span>
                  <span class="text-gray-700"
                    >Hỗ trợ in đa màu, gradient, hình ảnh phức tạp</span
                  >
                </div>
              </div>
            </div>
            <div class="gallery-item cursor-pointer" data-category="in-an">
              <div
                class="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center relative overflow-hidden group"
              >
                <img
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center"
                  alt="Chất lượng in ấn"
                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div
                  class="absolute inset-0 bg-black/20 flex items-center justify-center"
                >
                  <div class="text-center text-white">
                    <div class="text-4xl mb-2">🔍</div>
                    <p class="font-bold">Xem Mẫu In Chi Tiết</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center">
          <a
            href="#form-bao-gia"
            class="cta-button bg-red-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-red-600 inline-block"
          >
            🎨 Thiết Kế Túi Cho Tôi Ngay
          </a>
        </div>
      </div>
    </section>
    <!-- WHY CHOOSE BAG SECTION -->
    <section class="py-16 md:py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div class="container mx-auto px-4 sm:px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-2xl md:text-4xl font-bold">
            🎯 Tại Sao Túi Vải In Logo Lại Hiệu Quả Đến Vậy?
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
            Không phải ngẫu nhiên mà các thương hiệu lớn đều sử dụng túi vải làm
            quà tặng. Đây là chiến lược marketing thông minh!
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div
            class="bg-white p-6 rounded-xl shadow-lg text-center border border-blue-100"
          >
            <div
              class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl text-white">🎁</span>
            </div>
            <h4 class="font-bold text-lg mb-3 text-gray-800">
              Tạo Cảm Giác Được Tặng Quà
            </h4>
            <p class="text-gray-600 text-sm">
              Khách hàng cảm thấy được "ưu đãi đặc biệt" → Tăng thiện cảm với
              thương hiệu → Dễ chốt đơn hơn
            </p>
          </div>

          <div
            class="bg-white p-6 rounded-xl shadow-lg text-center border border-green-100"
          >
            <div
              class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl text-white">💰</span>
            </div>
            <h4 class="font-bold text-lg mb-3 text-gray-800">
              Tăng Giá Trị Cảm Nhận
            </h4>
            <p class="text-gray-600 text-sm">
              Đơn hàng 500k + túi đẹp = Cảm giác "được nhiều hơn trả" → Khách
              sẵn sàng mua thêm để đủ điều kiện
            </p>
          </div>

          <div
            class="bg-white p-6 rounded-xl shadow-lg text-center border border-purple-100"
          >
            <div
              class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl text-white">📢</span>
            </div>
            <h4 class="font-bold text-lg mb-3 text-gray-800">
              Quảng Bá Miễn Phí
            </h4>
            <p class="text-gray-600 text-sm">
              Khách dùng túi đi chợ, đi làm = Logo thương hiệu được nhìn thấy
              hàng trăm lần mỗi ngày
            </p>
          </div>

          <div
            class="bg-white p-6 rounded-xl shadow-lg text-center border border-yellow-100"
          >
            <div
              class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl text-white">🔄</span>
            </div>
            <h4 class="font-bold text-lg mb-3 text-gray-800">
              Khách Hàng Quay Lại
            </h4>
            <p class="text-gray-600 text-sm">
              Túi nhắc nhở thương hiệu mỗi khi sử dụng → Tăng tỷ lệ khách cũ mua
              lại → Giảm chi phí ads
            </p>
          </div>
        </div>

        <div
          class="bg-white p-8 rounded-2xl shadow-xl border border-gray-200 max-w-4xl mx-auto"
        >
          <div class="text-center mb-6">
            <h4 class="text-xl md:text-2xl font-bold text-gray-800 mb-3">
              💡 Công Thức Thành Công Đã Được Chứng Minh
            </h4>
          </div>
          <div class="grid md:grid-cols-3 gap-6 text-center">
            <div class="p-4 bg-red-50 rounded-lg">
              <div class="text-3xl font-bold text-red-600 mb-2">Bước 1</div>
              <p class="text-gray-700 font-semibold">
                Đặt điều kiện: "Đơn từ 500k tặng túi đẹp"
              </p>
            </div>
            <div class="p-4 bg-blue-50 rounded-lg">
              <div class="text-3xl font-bold text-blue-600 mb-2">Bước 2</div>
              <p class="text-gray-700 font-semibold">
                Khách mua thêm để đủ 500k nhận túi
              </p>
            </div>
            <div class="p-4 bg-green-50 rounded-lg">
              <div class="text-3xl font-bold text-green-600 mb-2">Kết Quả</div>
              <p class="text-gray-700 font-semibold">
                Tỷ lệ chốt ↗️ Giá trị đơn ↗️ Lợi nhuận ↗️
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- BUSINESS BENEFITS SECTION -->
    <section class="py-16 md:py-24 bg-white">
      <div class="container mx-auto px-4 sm:px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-2xl md:text-4xl font-bold">
            💼 Lợi Ích Cụ Thể Cho Shop Online
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
            Túi vải in logo không chỉ là quà tặng, mà là công cụ marketing mạnh
            mẽ giúp shop tăng doanh thu bền vững.
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8 mb-12">
          <!-- Benefit 1: Increase Conversion -->
          <div
            class="bg-gradient-to-br from-red-50 to-pink-50 p-8 rounded-2xl shadow-lg border border-red-100 text-center"
          >
            <div
              class="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-3xl text-white">📈</span>
            </div>
            <h4 class="text-xl font-bold text-gray-800 mb-4">
              Tăng Tỷ Lệ Chốt Đơn
            </h4>
            <div class="text-3xl font-bold text-red-600 mb-3">+15-30%</div>
            <p class="text-gray-600 mb-4">
              Khách thấy có quà tặng → Tăng động lực mua hàng → Giảm tỷ lệ bỏ
              giỏ hàng
            </p>
            <div class="bg-white p-4 rounded-lg">
              <p class="text-sm text-gray-700">
                <strong>Ví dụ:</strong> Shop có 1000 lượt truy cập/ngày, tỷ lệ
                chốt từ 2% → 2.6% = thêm 6 đơn/ngày
              </p>
            </div>
          </div>

          <!-- Benefit 2: Increase AOV -->
          <div
            class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl shadow-lg border border-blue-100 text-center"
          >
            <div
              class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-3xl text-white">💰</span>
            </div>
            <h4 class="text-xl font-bold text-gray-800 mb-4">
              Tăng Giá Trị Đơn Hàng
            </h4>
            <div class="text-3xl font-bold text-blue-600 mb-3">+10-25%</div>
            <p class="text-gray-600 mb-4">
              Đặt điều kiện "Đơn từ 500k tặng túi" → Khách mua thêm để đủ điều
              kiện
            </p>
            <div class="bg-white p-4 rounded-lg">
              <p class="text-sm text-gray-700">
                <strong>Ví dụ:</strong> AOV từ 420k → 520k, với 100 đơn/tháng =
                thêm 10 triệu doanh thu
              </p>
            </div>
          </div>

          <!-- Benefit 3: Brand Awareness -->
          <div
            class="bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-2xl shadow-lg border border-green-100 text-center"
          >
            <div
              class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <span class="text-3xl text-white">🎯</span>
            </div>
            <h4 class="text-xl font-bold text-gray-800 mb-4">
              Quảng Bá Thương Hiệu
            </h4>
            <div class="text-3xl font-bold text-green-600 mb-3">24/7</div>
            <p class="text-gray-600 mb-4">
              Khách dùng túi hàng ngày → Logo được nhìn thấy liên tục → Tăng
              nhận diện thương hiệu
            </p>
            <div class="bg-white p-4 rounded-lg">
              <p class="text-sm text-gray-700">
                <strong>Hiệu quả:</strong> 1 túi = 1000+ lần hiển thị logo trong
                6 tháng, chi phí chỉ 35k
              </p>
            </div>
          </div>
        </div>

        <!-- ROI Calculator -->
        <div
          class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-8 border border-yellow-200"
        >
          <div class="text-center mb-6">
            <h4 class="text-2xl font-bold text-gray-800 mb-3">
              🧮 Tính Toán ROI Đơn Giản
            </h4>
            <p class="text-gray-600">
              Xem túi vải mang lại lợi nhuận như thế nào cho shop của bạn
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-8 items-center">
            <div class="space-y-4">
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex justify-between items-center">
                  <span class="text-gray-700">Chi phí túi (35k/túi):</span>
                  <span class="font-bold text-red-600">-3.5 triệu</span>
                </div>
              </div>
              <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex justify-between items-center">
                  <span class="text-gray-700">Tăng doanh thu (100 túi):</span>
                  <span class="font-bold text-green-600">+15 triệu</span>
                </div>
              </div>
              <div
                class="bg-white p-4 rounded-lg shadow-sm border-2 border-green-500"
              >
                <div class="flex justify-between items-center">
                  <span class="text-gray-700 font-bold">Lợi nhuận ròng:</span>
                  <span class="font-bold text-green-600 text-xl"
                    >+11.5 triệu</span
                  >
                </div>
              </div>
            </div>

            <div class="text-center">
              <div class="text-6xl font-bold text-green-600 mb-2">329%</div>
              <div class="text-xl font-bold text-gray-800 mb-4">
                ROI (Return on Investment)
              </div>
              <p class="text-gray-600 mb-6">Đầu tư 1 đồng, thu về 3.29 đồng</p>
              <a
                href="#form-bao-gia"
                class="cta-button bg-green-500 text-white font-bold py-3 px-6 rounded-full hover:bg-green-600 inline-block"
              >
                💡 Tính ROI Cho Shop Tôi
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CASE STUDY SECTION -->
    <section id="case-study" class="py-16 md:py-24 bg-white">
      <div class="container mx-auto px-4 sm:px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-2xl md:text-4xl font-bold">
            📊 Case Thực Tế: Túi Vải Giúp Shop Tăng Doanh Thu
          </h3>
          <p class="mt-4 text-gray-600 max-w-3xl mx-auto text-base md:text-lg">
            Đây không phải lý thuyết! Đây là kết quả thực tế từ khách hàng đang
            kinh doanh online như bạn.
          </p>
        </div>

        <!-- Stats Overview -->
        <div
          class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto"
        >
          <div class="text-center p-4 bg-red-50 rounded-lg">
            <p class="text-2xl md:text-3xl font-bold text-red-600">500+</p>
            <p class="text-sm md:text-base text-gray-600">Shop đã áp dụng</p>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <p class="text-2xl md:text-3xl font-bold text-green-600">15-30%</p>
            <p class="text-sm md:text-base text-gray-600">Tỷ lệ chốt tăng</p>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <p class="text-2xl md:text-3xl font-bold text-blue-600">10-25%</p>
            <p class="text-sm md:text-base text-gray-600">AOV tăng</p>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <p class="text-2xl md:text-3xl font-bold text-purple-600">100K+</p>
            <p class="text-sm md:text-base text-gray-600">Túi đã sản xuất</p>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-6 md:gap-8 max-w-6xl mx-auto">
          <div
            class="bg-gradient-to-br from-pink-50 to-red-50 p-6 md:p-8 rounded-xl shadow-lg border border-red-100"
          >
            <div class="flex items-center mb-4">
              <div
                class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4"
              >
                💄
              </div>
              <div>
                <p class="text-lg font-semibold text-red-600">
                  Shop Mỹ Phẩm Online
                </p>
                <p class="text-sm text-gray-500">Doanh thu 2-3 tỷ/tháng</p>
              </div>
            </div>
            <div class="mb-4">
              <p class="text-3xl md:text-4xl font-extrabold text-gray-800">
                20.000+ túi
              </p>
              <p class="text-sm text-gray-600">Đã đặt trong 6 tháng</p>
            </div>
            <div class="bg-white/70 p-4 rounded-lg mb-4">
              <p class="text-gray-700 text-sm md:text-base">
                <strong
                  >"Trước đây tỷ lệ chốt đơn chỉ 2.5%, sau khi tặng túi cho đơn
                  từ 500k thì tăng lên 3.8%. Quan trọng hơn, khách mua thêm son,
                  kem để đủ 500k nhận túi → Giá trị đơn hàng tăng từ 420k lên
                  580k."</strong
                >
              </p>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="text-center p-3 bg-white rounded-lg">
                <p class="text-lg font-bold text-green-600">+52%</p>
                <p class="text-xs text-gray-600">Tỷ lệ chốt tăng</p>
              </div>
              <div class="text-center p-3 bg-white rounded-lg">
                <p class="text-lg font-bold text-blue-600">+38%</p>
                <p class="text-xs text-gray-600">Giá trị đơn tăng</p>
              </div>
            </div>
            <div class="flex flex-wrap gap-2">
              <span
                class="inline-block bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Tăng tỷ lệ chốt</span
              >
              <span
                class="inline-block bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Khách mua thêm</span
              >
              <span
                class="inline-block bg-purple-100 text-purple-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Quảng bá thương hiệu</span
              >
            </div>
          </div>

          <div
            class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 md:p-8 rounded-xl shadow-lg border border-blue-100"
          >
            <div class="flex items-center mb-4">
              <div
                class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4"
              >
                👗
              </div>
              <div>
                <p class="text-lg font-semibold text-blue-600">
                  Shop Thời Trang Nữ
                </p>
                <p class="text-sm text-gray-500">Doanh thu 4-5 tỷ/tháng</p>
              </div>
            </div>
            <div class="mb-4">
              <p class="text-3xl md:text-4xl font-extrabold text-gray-800">
                3.000 túi/tháng
              </p>
              <p class="text-sm text-gray-600">Đặt đều đặn 4 tháng</p>
            </div>
            <div class="bg-white/70 p-4 rounded-lg mb-4">
              <p class="text-gray-700 text-sm md:text-base">
                <strong
                  >"Khách thấy có túi đẹp làm quà → sẵn sàng mua thêm áo, váy để
                  đủ điều kiện. Chi phí túi chỉ 35k/cái nhưng giúp tăng giá trị
                  đơn hàng thêm 150k. Hiệu quả đầu tư rất cao!"</strong
                >
              </p>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="text-center p-3 bg-white rounded-lg">
                <p class="text-lg font-bold text-green-600">+25%</p>
                <p class="text-xs text-gray-600">Doanh thu tăng</p>
              </div>
              <div class="text-center p-3 bg-white rounded-lg">
                <p class="text-lg font-bold text-purple-600">4.3x</p>
                <p class="text-xs text-gray-600">Hiệu quả đầu tư</p>
              </div>
            </div>
            <div class="flex flex-wrap gap-2">
              <span
                class="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Tăng giá trị đơn</span
              >
              <span
                class="inline-block bg-yellow-100 text-yellow-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Hiệu quả cao</span
              >
              <span
                class="inline-block bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full"
                >Khách trung thành</span
              >
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <div
            class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 max-w-3xl mx-auto"
          >
            <p class="text-lg font-semibold text-yellow-800 mb-2">
              🎯 Công thức thành công
            </p>
            <p class="text-gray-700">
              <strong>Đơn hàng từ 500k-1 triệu → Tặng túi vải in logo</strong
              ><br />
              Chi phí túi = 5-8% giá trị đơn hàng | Lợi nhuận tăng = 15-30%
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- ABOUT FACTORY SECTION -->
    <section class="py-16 md:py-24 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-12">
          <h3 class="section-title text-3xl md:text-4xl font-bold">
            Xưởng Sản Xuất Loma Bag
          </h3>
          <p class="mt-4 text-gray-600 max-w-2xl mx-auto">
            Sản xuất trực tiếp - không qua trung gian. Đảm bảo chất lượng và giá
            thành tốt nhất cho bạn.
          </p>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="bg-white p-6 rounded-lg shadow-md text-center">
            <h4 class="font-bold text-xl mb-2">Máy Móc Hiện Đại</h4>
            <p class="text-gray-600">
              Hệ thống máy may, máy in công nghệ cao, đảm bảo sản phẩm đồng đều,
              đường may chắc chắn, hình in sắc nét.
            </p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md text-center">
            <h4 class="font-bold text-xl mb-2">Đội Ngũ Lành Nghề</h4>
            <p class="text-gray-600">
              Thợ may, thợ in có kinh nghiệm lâu năm, tỉ mỉ trong từng công đoạn
              để tạo ra sản phẩm hoàn hảo nhất.
            </p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md text-center">
            <h4 class="font-bold text-xl mb-2">Kiểm Soát Chất Lượng</h4>
            <p class="text-gray-600">
              Quy trình KCS (Kiểm tra chất lượng sản phẩm) nghiêm ngặt từ khâu
              chọn vải, cắt, in, may cho đến đóng gói.
            </p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md text-center">
            <h4 class="font-bold text-xl mb-2">Năng Lực Sản Xuất Lớn</h4>
            <p class="text-gray-600">
              Sẵn sàng đáp ứng các đơn hàng từ số lượng nhỏ 50 túi đến hàng chục
              nghìn túi, đảm bảo tiến độ giao hàng.
            </p>
          </div>
        </div>
        <div class="text-center mt-12">
          <a
            href="#video-section"
            class="cta-button bg-white text-gray-800 border border-gray-300 font-bold py-3 px-8 rounded-full hover:bg-gray-100"
          >
            Xem Video Xưởng Sản Xuất
          </a>
        </div>
      </div>
    </section>

    <!-- FORM SECTION -->
    <section
      id="form-bao-gia"
      class="py-16 md:py-24 bg-gradient-to-br from-gray-800 to-gray-900 text-white"
    >
      <div class="container mx-auto px-4 sm:px-6">
        <div class="max-w-3xl mx-auto">
          <div class="text-center mb-10">
            <h3 class="text-2xl md:text-4xl font-bold mb-4">
              🚀 Bắt Đầu Ngay - Nhận Mockup Miễn Phí!
            </h3>
            <p class="text-lg md:text-xl text-gray-300 mb-6">
              Chỉ cần 2 phút để điền form → Nhận thiết kế mockup túi với logo
              của bạn trong vòng 2 giờ!
            </p>
            <div class="flex flex-wrap justify-center gap-4 text-sm">
              <span
                class="bg-green-500/20 text-green-300 px-3 py-1 rounded-full"
                >✅ Mockup miễn phí</span
              >
              <span class="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full"
                >✅ Tư vấn qua Zalo</span
              >
              <span
                class="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full"
                >✅ Báo giá chính xác</span
              >
            </div>
          </div>

          <div
            class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-white/20"
          >
            <form action="#" method="POST" class="space-y-6">
              <div class="grid md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-300 mb-2"
                    >👤 Tên của bạn *</label
                  >
                  <input
                    type="text"
                    name="name"
                    id="name"
                    required
                    class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                    placeholder="Ví dụ: Nguyễn Văn A"
                  />
                </div>
                <div>
                  <label
                    for="phone"
                    class="block text-sm font-medium text-gray-300 mb-2"
                    >📱 Số Zalo *</label
                  >
                  <input
                    type="tel"
                    name="phone"
                    id="phone"
                    required
                    class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                    placeholder="0901234567"
                  />
                </div>
              </div>

              <div>
                <label
                  for="business"
                  class="block text-sm font-medium text-gray-300 mb-2"
                  >🏪 Bạn bán gì? *</label
                >
                <select
                  name="business"
                  id="business"
                  required
                  class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white"
                >
                  <option value="">Chọn ngành hàng của bạn</option>
                  <option value="my-pham">Mỹ phẩm</option>
                  <option value="thoi-trang">Thời trang</option>
                  <option value="phu-kien">Phụ kiện</option>
                  <option value="do-gia-dung">Đồ gia dụng</option>
                  <option value="thuc-pham">Thực phẩm</option>
                  <option value="khac">Khác</option>
                </select>
              </div>

              <div>
                <label
                  for="package"
                  class="block text-sm font-medium text-gray-300 mb-2"
                  >📦 Bạn quan tâm gói nào? *</label
                >
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <label
                    class="flex items-center p-4 bg-white/5 rounded-lg border border-white/20 cursor-pointer hover:bg-white/10 transition-colors"
                  >
                    <input
                      type="radio"
                      name="package"
                      value="test"
                      required
                      class="mr-3 text-red-500"
                    />
                    <div>
                      <div class="font-semibold text-red-300">
                        🎯 Gói Thử Nghiệm
                      </div>
                      <div class="text-sm text-gray-400">
                        Mockup + Mẫu miễn phí
                      </div>
                    </div>
                  </label>
                  <label
                    class="flex items-center p-4 bg-white/5 rounded-lg border border-white/20 cursor-pointer hover:bg-white/10 transition-colors"
                  >
                    <input
                      type="radio"
                      name="package"
                      value="scale"
                      required
                      class="mr-3 text-blue-500"
                    />
                    <div>
                      <div class="font-semibold text-blue-300">
                        🚀 Gói Scale Up
                      </div>
                      <div class="text-sm text-gray-400">
                        Giá sỉ, số lượng lớn
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <div>
                <label
                  for="notes"
                  class="block text-sm font-medium text-gray-300 mb-2"
                  >💭 Ghi chú thêm (không bắt buộc)</label
                >
                <textarea
                  name="notes"
                  id="notes"
                  rows="3"
                  class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/30 focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                  placeholder="Ví dụ: Muốn túi màu đỏ, in logo 2 mặt, dự kiến đặt 200 túi..."
                ></textarea>
              </div>

              <div class="text-center pt-4">
                <button
                  type="submit"
                  class="cta-button w-full bg-gradient-to-r from-red-500 to-red-600 text-white font-bold py-4 px-8 rounded-full text-lg hover:from-red-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300"
                >
                  🎁 NHẬN MOCKUP MIỄN PHÍ NGAY
                </button>
                <p class="text-sm text-gray-400 mt-3">
                  ⚡ Phản hồi trong 2 giờ | 🔒 Thông tin được bảo mật
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- FOOTER -->
    <footer class="bg-gray-800 text-white py-12">
      <div class="container mx-auto px-6">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="md:col-span-2">
            <h3 class="text-2xl font-bold mb-4">
              Loma<span class="text-red-500">Bag</span>
            </h3>
            <p class="text-gray-300 mb-4 leading-relaxed">
              Xưởng sản xuất túi vải in logo chuyên nghiệp, phục vụ hơn 500+
              shop online trên toàn quốc. Cam kết chất lượng cao, giá cả hợp lý,
              giao hàng đúng hẹn.
            </p>
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <span class="text-green-400 mr-2">📞</span>
                <span class="font-semibold">0901.234.567</span>
              </div>
              <div class="flex items-center">
                <span class="text-blue-400 mr-2">📧</span>
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h4 class="text-lg font-semibold mb-4">Liên Kết Nhanh</h4>
            <ul class="space-y-2">
              <li>
                <a
                  href="#case-study"
                  class="text-gray-300 hover:text-white transition-colors"
                  >Case Study</a
                >
              </li>
              <li>
                <a
                  href="#video-section"
                  class="text-gray-300 hover:text-white transition-colors"
                  >Video Xưởng</a
                >
              </li>
              <li>
                <a
                  href="#gallery"
                  class="text-gray-300 hover:text-white transition-colors"
                  >Thư Viện Mẫu</a
                >
              </li>
              <li>
                <a
                  href="#pricing"
                  class="text-gray-300 hover:text-white transition-colors"
                  >Bảng Giá</a
                >
              </li>
            </ul>
          </div>

          <!-- Services -->
          <div>
            <h4 class="text-lg font-semibold mb-4">Dịch Vụ</h4>
            <ul class="space-y-2 text-gray-300">
              <li>✅ Thiết kế mockup miễn phí</li>
              <li>✅ Sản xuất từ 50 túi</li>
              <li>✅ In logo chất lượng cao</li>
              <li>✅ Giao hàng toàn quốc</li>
              <li>✅ Bảo hành chất lượng</li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-700 mt-8 pt-8 text-center">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-400 text-sm">
              © 2024 LomaBag. Tất cả quyền được bảo lưu.
            </p>
            <div class="flex items-center space-x-4 mt-4 md:mt-0">
              <span class="text-gray-400 text-sm"
                >Được tin tưởng bởi 500+ shop online</span
              >
              <div class="flex space-x-1">
                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- MODALS -->
    <div
      id="gallery-modal"
      class="modal fixed inset-0 bg-black bg-opacity-75 items-center justify-center z-50"
      style="display: none"
    >
      <div
        class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-w-4xl relative p-4"
      >
        <button
          class="close-modal-btn absolute -top-3 -right-3 w-10 h-10 bg-red-500 text-white rounded-full text-2xl z-10"
        >
          &times;
        </button>
        <h3 id="modal-title" class="text-2xl font-bold mb-4 text-center"></h3>
        <div class="swiper-container">
          <div class="swiper-wrapper"></div>
          <div class="swiper-pagination"></div>
          <div class="swiper-button-next text-red-500"></div>
          <div class="swiper-button-prev text-red-500"></div>
        </div>
      </div>
    </div>

    <div
      id="video-modal"
      class="modal fixed inset-0 bg-black bg-opacity-80 items-center justify-center z-50"
      style="display: none"
    >
      <div class="w-11/12 md:w-3/4 lg:w-2/3 max-w-4xl relative">
        <button
          class="close-modal-btn absolute -top-10 right-0 w-10 h-10 text-white text-4xl"
        >
          &times;
        </button>
        <div id="video-player" class="aspect-video"></div>
      </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/typed.js@2.0.12"></script>
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Ensure modals are hidden on page load
        const modals = document.querySelectorAll(".modal");
        modals.forEach((modal) => {
          modal.style.display = "none";
          modal.style.opacity = "0";
        });
        // --- TYPING EFFECT ---
        new Typed("#typing-effect", {
          strings: [
            "tăng 15-30% tỷ lệ chốt đơn?",
            "tăng 10-25% giá trị đơn hàng?",
            "khiến khách nhớ thương hiệu?",
            "tạo lý do để khách mua thêm?",
            "biến khách thành fan trung thành?",
          ],
          typeSpeed: 60,
          backSpeed: 40,
          backDelay: 1500,
          startDelay: 500,
          loop: true,
          smartBackspace: true,
          showCursor: true,
          cursorChar: "|",
        });

        // --- GALLERY MODAL & SWIPER ---
        const galleryData = {
          tote: {
            title: "Túi Tote Canvas - Phổ Biến Nhất",
            images: [
              "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1591195853828-11db59a44f6b?w=800&h=600&fit=crop&crop=center",
            ],
          },
          "day-rut": {
            title: "Túi Dây Rút Thể Thao",
            images: [
              "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1591195853828-11db59a44f6b?w=800&h=600&fit=crop&crop=center",
            ],
          },
          hop: {
            title: "Túi Hộp Cao Cấp",
            images: [
              "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1591195853828-11db59a44f6b?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&h=600&fit=crop&crop=center",
            ],
          },
          "in-an": {
            title: "Chất Lượng In Ấn Sắc Nét",
            images: [
              "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=800&h=600&fit=crop&crop=center",
              "https://images.unsplash.com/photo-1591195853828-11db59a44f6b?w=800&h=600&fit=crop&crop=center",
            ],
          },
        };

        const galleryModal = document.getElementById("gallery-modal");
        const galleryModalTitle = document.getElementById("modal-title");
        const swiperWrapper = galleryModal.querySelector(".swiper-wrapper");

        // Initialize swiper variable
        let swiper = null;

        document.querySelectorAll(".gallery-item").forEach((item) => {
          item.addEventListener("click", (e) => {
            e.preventDefault();

            const category = item.dataset.category;
            const data = galleryData[category];

            if (!data) {
              console.error("No data found for category:", category);
              return;
            }

            console.log("Gallery item clicked:", category);

            if (galleryModalTitle) {
              galleryModalTitle.textContent = data.title;
            }

            if (swiperWrapper) {
              swiperWrapper.innerHTML = "";

              // Add slides
              data.images.forEach((imgUrl) => {
                const slide = document.createElement("div");
                slide.classList.add("swiper-slide");
                slide.innerHTML = `<img src="${imgUrl}" class="w-full h-full object-contain rounded-md" style="max-height: 70vh;">`;
                swiperWrapper.appendChild(slide);
              });
            }

            // Destroy existing swiper if it exists
            if (swiper) {
              swiper.destroy(true, true);
              swiper = null;
            }

            // Open modal first
            openModal(galleryModal);

            // Initialize new swiper after modal is opened
            setTimeout(() => {
              if (typeof Swiper !== "undefined") {
                swiper = new Swiper(".swiper-container", {
                  loop: data.images.length > 1,
                  pagination: { el: ".swiper-pagination", clickable: true },
                  navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                  },
                  slidesPerView: 1,
                  spaceBetween: 10,
                });
              }
            }, 200);
          });
        });

        // --- VIDEO MODAL ---
        const videoModal = document.getElementById("video-modal");
        const videoPlayer = document.getElementById("video-player");

        document.querySelectorAll(".video-placeholder").forEach((item) => {
          item.addEventListener("click", (e) => {
            e.preventDefault();

            const videoId = item.dataset.videoId;
            console.log("Video clicked:", videoId);

            if (!videoId) {
              console.error("No video ID found");
              return;
            }

            if (videoPlayer) {
              videoPlayer.innerHTML = `<iframe class="w-full h-full" src="https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
            }

            openModal(videoModal);
          });
        });

        // --- COMMON MODAL LOGIC ---
        function openModal(modal) {
          if (!modal) return;

          // Ensure modal is visible
          modal.style.display = "flex";
          modal.style.visibility = "visible";
          modal.style.opacity = "0";

          // Force reflow
          modal.offsetHeight;

          // Animate in
          modal.style.transition = "opacity 0.3s ease";
          modal.style.opacity = "1";

          // Prevent body scroll
          document.body.style.overflow = "hidden";

          console.log("Modal opened:", modal.id);
        }

        function closeModal(modal) {
          if (!modal) return;

          modal.style.transition = "opacity 0.3s ease";
          modal.style.opacity = "0";

          setTimeout(() => {
            modal.style.display = "none";
            modal.style.visibility = "hidden";

            // Stop video when closing
            if (modal.id === "video-modal") {
              const videoPlayer = document.getElementById("video-player");
              if (videoPlayer) {
                videoPlayer.innerHTML = "";
              }
            }

            // Destroy swiper when closing gallery modal
            if (modal.id === "gallery-modal" && swiper) {
              swiper.destroy(true, true);
              swiper = null;
            }

            // Restore body scroll
            document.body.style.overflow = "";

            console.log("Modal closed:", modal.id);
          }, 300);
        }

        document.querySelectorAll(".close-modal-btn").forEach((btn) => {
          btn.addEventListener("click", () => {
            closeModal(btn.closest(".modal"));
          });
        });

        document.querySelectorAll(".modal").forEach((modal) => {
          modal.addEventListener("click", (e) => {
            if (e.target === modal) {
              closeModal(modal);
            }
          });
        });

        // Close modal with Escape key
        document.addEventListener("keydown", function (e) {
          if (e.key === "Escape") {
            const openModalEl = document.querySelector('.modal[style*="flex"]');
            if (openModalEl) {
              closeModal(openModalEl);
            }
          }
        });

        // --- SMOOTH SCROLL FOR NAVIGATION ---
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
          anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
              const headerHeight = 80; // Account for fixed header
              const targetPosition = target.offsetTop - headerHeight;

              window.scrollTo({
                top: targetPosition,
                behavior: "smooth",
              });
            }
          });
        });

        // --- SCROLL TO TOP BUTTON ---
        const scrollToTopBtn = document.createElement("button");
        scrollToTopBtn.innerHTML = "↑";
        scrollToTopBtn.className =
          "fixed bottom-6 right-6 w-12 h-12 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-all duration-300 z-40 opacity-0 pointer-events-none";
        scrollToTopBtn.style.transform = "translateY(100px)";
        document.body.appendChild(scrollToTopBtn);

        scrollToTopBtn.addEventListener("click", () => {
          window.scrollTo({ top: 0, behavior: "smooth" });
        });

        // Show/hide scroll to top button
        window.addEventListener("scroll", () => {
          if (window.scrollY > 500) {
            scrollToTopBtn.style.opacity = "1";
            scrollToTopBtn.style.pointerEvents = "auto";
            scrollToTopBtn.style.transform = "translateY(0)";
          } else {
            scrollToTopBtn.style.opacity = "0";
            scrollToTopBtn.style.pointerEvents = "none";
            scrollToTopBtn.style.transform = "translateY(100px)";
          }
        });
      });
    </script>
  </body>
</html>
